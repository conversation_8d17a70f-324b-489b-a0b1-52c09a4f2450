import pandas as pd
import openpyxl
from datetime import datetime
import os

def process_serentica_data():
    # File paths
    bikaner_file = r"c:\Users\<USER>\Desktop\Code\Serentica_REMC_RTM_Mailer\Bikaner Serentica - Check sheet.xlsx"
    mpl_file = r"c:\Users\<USER>\Desktop\Code\Serentica_REMC_RTM_Mailer\MPL RTM format.xlsm"
    remc_file = r"c:\Users\<USER>\Desktop\Code\Serentica_REMC_RTM_Mailer\REMC FORMAT.csv"
    
    # Read Bikaner Serentica file
    bikaner_wb = openpyxl.load_workbook(bikaner_file, data_only=True)  # data_only=True to get calculated values instead of formulas
    bikaner_ws = bikaner_wb['REMC']  # Use the REMC sheet specifically
    
    # 1. Fetch dynamic table data (row 1 till continuous values)
    main_data = []
    headers = []
    
    # Get headers from row 1
    for col in range(1, bikaner_ws.max_column + 1):
        cell_value = bikaner_ws.cell(1, col).value
        if cell_value is not None:
            headers.append(cell_value)
        else:
            break
    
    # Get data rows until no continuous values
    for row in range(2, bikaner_ws.max_row + 1):
        row_data = []
        has_data = False
        for col in range(1, len(headers) + 1):
            cell_value = bikaner_ws.cell(row, col).value
            row_data.append(cell_value)
            if cell_value is not None and cell_value != 0:
                has_data = True
        
        if has_data:
            main_data.append(row_data)
        else:
            break
    
    # Create DataFrame
    df_main = pd.DataFrame(main_data, columns=headers)


    
    # 2. Filter columns based on rows 15-110 data, but keep entire structure
    data_rows = df_main.iloc[14:110].copy()  # rows 15-110 (0-indexed) for filtering

    # Remove columns that have only 0 values in the data rows (15-110)
    cols_to_keep = []
    for col in df_main.columns:
        # Check if column has any non-zero values in rows 15-110 (handling different data types)
        try:
            # Convert to numeric, coercing errors to NaN
            numeric_col = pd.to_numeric(data_rows[col], errors='coerce')
            # Keep column if it has any non-zero, non-null values in data rows
            if not (numeric_col.fillna(0) == 0).all():
                cols_to_keep.append(col)
        except:
            # If conversion fails completely, keep the column (likely contains text)
            cols_to_keep.append(col)

    # Keep the entire structure (all rows) but only the filtered columns
    df_filtered = df_main[cols_to_keep].copy()




    
    # 3. Insert into REMC FORMAT starting from row 7
    remc_df = pd.read_csv(remc_file)
    
    # Update date and revision number correctly
    today_date = datetime.now().strftime("%d-%m-%Y")
    remc_df.iloc[1, 2] = today_date  # Date row (row index 1)
    remc_df.iloc[2, 2] = "INTRADAY"  # Revision No row (row index 2)
    
    # Insert filtered data starting from row 7 (index 6)
    # df_filtered now contains the complete structure (headers + data)
    # Skip the first column (index 0) as it contains labels that are already in column A
    for i in range(len(df_filtered)):
        if i + 6 < len(remc_df):
            for j in range(1, len(cols_to_keep)):  # Start from index 1 to skip first column
                if j < len(remc_df.columns):  # Adjust column index since we're skipping first column
                    # Get the actual value using iloc to avoid Series issues
                    value = df_filtered.iloc[i, j]
                    # Convert to string if it's not None, otherwise use empty string
                    if value is not None and str(value) != 'nan':
                        remc_df.iloc[i + 6, j] = str(value)  # Use j instead of j+1 since we're starting from 1
                    else:
                        remc_df.iloc[i + 6, j] = ""


    
    # Save updated REMC FORMAT
    try:
        remc_df.to_csv(remc_file, index=False)
        print("REMC FORMAT.csv updated successfully")
    except PermissionError:
        print("ERROR: Cannot save REMC FORMAT.csv - file may be open in Excel. Please close the file and try again.")
        return
    
    # 4. Process RTM data (columns J-M, rows 15-110)
    rtm_data = []
    for row in range(15, 111):  # rows 15-110
        time_block = bikaner_ws.cell(row, 10).value  # Column J
        rtm_value = bikaner_ws.cell(row, 11).value   # Column K
        modified_rtm = bikaner_ws.cell(row, 12).value  # Column L
        price = bikaner_ws.cell(row, 13).value       # Column M
        
        # Use Modified RTM if available, otherwise use RTM
        final_rtm = modified_rtm if modified_rtm is not None else rtm_value
        
        rtm_data.append({
            'time_block': time_block,
            'final_rtm': final_rtm,
            'price': price
        })
    
    # 5. Update MPL RTM format
    mpl_wb = openpyxl.load_workbook(mpl_file, keep_vba=True)
    mpl_ws = mpl_wb.active
    
    # Set current date in merged cell D12:E12
    mpl_ws['D12'] = today_date
    
    # Insert RTM data into columns J and K from rows 21-116
    for i, data in enumerate(rtm_data):
        row_num = 21 + i  # Starting from row 21
        if row_num <= 116:
            mpl_ws.cell(row_num, 10, data['final_rtm'])  # Column J
            mpl_ws.cell(row_num, 11, data['price'])      # Column K
    
    # Save MPL RTM format
    try:
        mpl_wb.save(mpl_file)
        print("MPL RTM format.xlsm updated successfully")
    except PermissionError:
        print("ERROR: Cannot save MPL RTM format.xlsm - file may be open in Excel. Please close the file and try again.")
        return
    
    print("Data processing completed successfully!")
    print(f"- REMC FORMAT updated with date: {today_date}")
    print(f"- MPL RTM format updated with RTM data and date: {today_date}")

if __name__ == "__main__":
    process_serentica_data()